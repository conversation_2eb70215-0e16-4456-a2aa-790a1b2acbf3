import 'dart:convert';
import 'dart:io';
import 'package:anki_guru/controllers/video_notes/subtitle_controller.dart';
import 'package:anki_guru/controllers/video_notes/bilibili_api.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:media_kit_video/media_kit_video.dart'; // Provides [VideoController] & [Video] etc.
import 'package:media_kit/media_kit.dart'; // Provides [Player], [Media], [Playlist] etc.
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pick_or_save/pick_or_save.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/hotkey.dart';
import 'package:hotkey_manager/hotkey_manager.dart';
import 'package:flutter/services.dart';
import 'package:media_kit_video/media_kit_video_controls/src/controls/methods/video_state.dart';
import 'package:media_kit_video/media_kit_video_controls/src/controls/extensions/duration.dart';
import 'package:media_kit_video/media_kit_video_controls/src/controls/widgets/video_controls_theme_data_injector.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_controller.dart';
import 'package:anki_guru/controllers/anki/ocr/components/ocr_image.dart';

class VideoNoteController extends GetxController {
  // 已有数据
  final playerList = [
    {"label": "videoNotes.player.builtin".tr, "value": "builtin"},
    {"label": "videoNotes.player.browser".tr, "value": "browser"},
  ];
  final clozeModeList = [
    {"label": "videoNotes.cloze.maskOneGuessOne".tr, "value": "mask_one_guess_one"},
    {"label": "videoNotes.cloze.maskAllGuessOne".tr, "value": "mask_all_guess_one"},
    {"label": "videoNotes.cloze.maskAllGuessAll".tr, "value": "mask_all_guess_all"},
    {"label": "videoNotes.cloze.freeGuess".tr, "value": "free_guess"},
    {"label": "videoNotes.cloze.scratchGuess".tr, "value": "scratch_guess"},
  ];
  final linkFormatList = [
    {"label": "Markdown", "value": "markdown"},
    {"label": "URL", "value": "url"},
    {"label": "HTML", "value": "html"},
    {"label": "videoNotes.linkFormat.custom".tr, "value": "custom"},
  ];
   final linkProtocolList = [
    {"label": "guru2://", "value": "guru2://"},
    {"label": "http://", "value": "http://"},
  ];
  // 控制器
  final defaultPlayer = "builtin".obs;
  final player = Rx<Player>(Player());
  final videoState = Rx<VideoState>(VideoState());
  final isFullScreen = false.obs;
  final settingController = Get.find<SettingController>();
  final webviewController = Get.find<WebviewController>();
  final _storage = StorageManager();
  final hotkeyNames = [
    "insertPathLink",
    "insertTimestampLink",
    "insertScreenshot",
    "playOrPause",
    "forward5",
    "forward15",
    "backward5",
    "backward15",
    "annotScreenshot",
    "clozeScreenshot",
  ];
  final disableAllShortcuts = false.obs;
  final shortcutMap = <String, HotKey?>{}.obs;
  final shortcutHandlerMap = <String, Future<void> Function(HotKey)>{}.obs;
  // Observable variables
  final enableLongPressSpeed = true.obs;
  final longPressSpeedRate = 2.0.obs;
  final showMiniProgress = true.obs;
  final screenshotWithLink = false.obs;
  final linkPosition = 'bottom'.obs;
  final linkFormat = 'markdown'.obs;
  final linkProtocol = 'guru2://"'.obs;
  final customLinkTemplate = '[{title}]({url})'.obs;
  final defaultDeck = 'videoNotes.defaults.deckName'.tr.obs;
  final defaultTags = ''.obs;
  final clozeMode = "free_guess".obs;
  final oneClozePeCard = false.obs;
  final pauseAfterScreenshot = true.obs;
  final pauseAfterCopyLink = true.obs;
  final usePathEncoding = true.obs;
  final useRelativePath = false.obs;
  final isAutoPaste = true.obs;
  final rootDir = "".obs;
  final enableDoubleTapPause = true.obs;
  final enableDoubleTapSeek = true.obs;
  final seekSeconds = 10.obs;
  final enableBrightnessGesture = true.obs;
  final enableVolumeGesture = true.obs;

  // 错误
  final rootDirError = "".obs;
  // B站登录状态
  final _bilibiliAPI = BilibiliService();
  final RxBool isBiliLoggedIn = false.obs;
  final RxString biliCookie = ''.obs;
  final RxMap<String, dynamic> biliUserInfo = <String, dynamic>{}.obs;

  // VideoController getVideoController() => VideoController(player.value);
  late VideoController videoController;

  // 播放列表数据结构
  final RxList<Map<String, dynamic>> playlist = <Map<String, dynamic>>[].obs;
  final currentMediaIndex = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    loadSettings();
    videoController = VideoController(player.value);
    shortcutHandlerMap["insertPathLink"] = (hotKey) async {
      await getTimestampLink();
    };
    shortcutHandlerMap["insertTimestampLink"] = (hotKey) async {
      await getTimestampLink();
    };
    shortcutHandlerMap["insertScreenshot"] = (hotKey) async {
      await getScreenshot();
    };
    shortcutHandlerMap["playOrPause"] = (hotKey) async {
      await playOrPause();
    };
    shortcutHandlerMap["forward5"] = (hotKey) async {
      await seek("+5");
    };
    shortcutHandlerMap["forward15"] = (hotKey) async {
      await seek("+15");
    };
    shortcutHandlerMap["backward5"] = (hotKey) async {
      await seek("-5");
    };
    shortcutHandlerMap["backward15"] = (hotKey) async {
      await seek("-15");
    };
    shortcutHandlerMap["annotScreenshot"] = (hotKey) async {
      await annotSceenshot();
    };
    shortcutHandlerMap["clozeScreenshot"] = (hotKey) async {
      await clozeSceenshot();
    };
  }

  Future<void> initHotKey() async {
    if (!disableAllShortcuts.value) {
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        await initAllShortcutsHandler();
      }
    }
  }

  Future<String> validateRootDir(String value, List<String> files) async {
    if (value.isEmpty) {
      return 'videoNotes.validation.rootFolderEmpty'.tr;
    }
    if (value.isEmpty) {
      if (files.isEmpty) {
        return 'videoNotes.validation.selectDirectory'.tr;
      }
    }
    final path = PathUtils(value);
    if (!path.exists()) {
      return 'videoNotes.validation.directoryNotExists'.tr;
    }
    if (!path.isDir) {
      return 'videoNotes.validation.selectDirectoryNotFile'.tr;
    }
    return "";
  }

  void loadSettings() {
    // Load default player setting
    defaultPlayer.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.defaultPlayer, 'builtin');

    enableLongPressSpeed.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.longPressSpeedUp, true);

    showMiniProgress.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.showMiniProgressBar, true);

    longPressSpeedRate.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.defaultVideoSpeed, 2.0);

    screenshotWithLink.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.screenshotWithLink, false);

    linkPosition.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.linkPosition, 'bottom');

    linkFormat.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.linkFormat, 'markdown');

    customLinkTemplate.value = _storage.read(StorageBox.videoNotes,
        VideoNotesStorageKeys.customLinkTemplate, '[{title}]({url})');

    defaultDeck.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.defaultDeck, 'videoNotes.defaults.deckName'.tr);

    defaultTags.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.defaultTags, '');

    clozeMode.value = _storage.read(StorageBox.videoNotes,
        VideoNotesStorageKeys.defaultClozeMode, 'free_guess');

    oneClozePeCard.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.oneClozePeCard, false);

    pauseAfterScreenshot.value = _storage.read(StorageBox.videoNotes,
        VideoNotesStorageKeys.pauseAfterScreenshot, true);

    pauseAfterCopyLink.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.pauseAfterCopyLink, true);

    isAutoPaste.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.isAutoPaste, true);

    usePathEncoding.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.usePathEncoding, true);

    useRelativePath.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.useRelativePath, false);

    rootDir.value =
        _storage.read(StorageBox.videoNotes, VideoNotesStorageKeys.rootDir, "");

    enableDoubleTapPause.value = _storage.read(StorageBox.videoNotes,
        VideoNotesStorageKeys.enableDoubleTapPause, true);

    enableDoubleTapSeek.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.enableDoubleTapSeek, true);

    seekSeconds.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.seekSeconds, 10);

    enableBrightnessGesture.value = _storage.read(StorageBox.videoNotes,
        VideoNotesStorageKeys.enableBrightnessGesture, true);

    enableVolumeGesture.value = _storage.read(
        StorageBox.videoNotes, VideoNotesStorageKeys.enableVolumeGesture, true);
    initHotKey();
  }

  void saveSettings() {
    // Save default player setting
    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.defaultPlayer, defaultPlayer.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.longPressSpeedUp, enableLongPressSpeed.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.showMiniProgressBar, showMiniProgress.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.defaultVideoSpeed, longPressSpeedRate.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.screenshotWithLink, screenshotWithLink.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.linkPosition,
        linkPosition.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.linkFormat,
        linkFormat.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.customLinkTemplate, customLinkTemplate.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.defaultDeck,
        defaultDeck.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.defaultTags,
        defaultTags.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.defaultClozeMode, clozeMode.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.oneClozePeCard,
        oneClozePeCard.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.pauseAfterScreenshot, pauseAfterScreenshot.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.pauseAfterCopyLink, pauseAfterCopyLink.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.usePathEncoding,
        usePathEncoding.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.useRelativePath,
        useRelativePath.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.isAutoPaste,
        isAutoPaste.value);

    _storage.write(
        StorageBox.videoNotes, VideoNotesStorageKeys.rootDir, rootDir.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.enableDoubleTapPause, enableDoubleTapPause.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.enableDoubleTapSeek, enableDoubleTapSeek.value);

    _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.seekSeconds,
        seekSeconds.value);

    _storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.enableBrightnessGesture,
        enableBrightnessGesture.value);

    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.enableVolumeGesture, enableVolumeGesture.value);

    Get.showSnackbar(
      GetSnackBar(
        message: 'videoNotes.message.settingsSaved'.tr,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 更新默认播放器设置并立即保存
  void updateDefaultPlayer(String playerType) {
    final timestamp = DateTime.now().toIso8601String();
    logger.i('[$timestamp] 更新默认播放器设置: $playerType');

    defaultPlayer.value = playerType;

    // 立即保存到存储
    _storage.write(StorageBox.videoNotes,
        VideoNotesStorageKeys.defaultPlayer, defaultPlayer.value);

    logger.i('[$timestamp] 默认播放器设置已保存: ${defaultPlayer.value}');

    // 使用简单的snackbar而不是系统通知
    Get.showSnackbar(
      GetSnackBar(
        message: 'videoNotes.notification.playerSwitchedTo'.trParams({'player': playerType == "builtin" ? "videoNotes.player.builtin".tr : "videoNotes.player.browser".tr}),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String getCurrentVideoUrl() {
    if (defaultPlayer.value == "browser") {
      // 在浏览器模式下，返回当前URL
      // 这里不需要使用webviewController，因为浏览器插件会自动连接到Flutter应用的WebSocket服务器
      // 浏览器插件会通过WebSocket发送当前URL
      return "";
    }
    logger.i('Current media playlist: ${playlist.value}');
    logger.i('Current media index: ${currentMediaIndex.value}');
    if (playlist.value.isEmpty) {
      return "";
    }
    return playlist.value[currentMediaIndex.value]['url'] ?? "";
  }

  /// 根据useRelativePath设置转换视频路径
  /// 如果useRelativePath为true，将本地文件的绝对路径转换为相对于rootDir的相对路径
  /// 如果useRelativePath为false，返回原始路径
  /// 在线视频URL（http://、https://等）始终保持原样，不进行路径转换
  String _convertVideoPathForLink(String videoPath) {
    // 检查是否为在线视频URL
    if (_isOnlineVideoUrl(videoPath)) {
      logger.d('Detected online video URL, using original URL: $videoPath');
      return videoPath;
    }

    logger.d('Detected local video file, applying path conversion logic: $videoPath');

    // 如果不使用相对路径，直接返回原始路径
    if (!useRelativePath.value) {
      logger.d('useRelativePath is false, using absolute path: $videoPath');
      return videoPath;
    }

    // 如果rootDir为空，返回原始路径
    if (rootDir.value.isEmpty) {
      logger.w('useRelativePath is true but rootDir is empty, using absolute path for local file: $videoPath');
      return videoPath;
    }

    // 检查本地视频文件路径是否在rootDir内
    final rootDirPath = path.normalize(rootDir.value);
    final normalizedVideoPath = path.normalize(videoPath);

    if (normalizedVideoPath.startsWith(rootDirPath)) {
      // 计算相对路径
      final relativePath = path.relative(normalizedVideoPath, from: rootDirPath);
      logger.d('Converted local file absolute path "$videoPath" to relative path "$relativePath"');
      return '$relativePath';
    } else {
      // 如果本地视频文件不在rootDir内，返回原始路径并记录警告
      logger.w('Local video file "$videoPath" is not within rootDir "$rootDirPath", using absolute path');
      return videoPath;
    }
  }

  /// 检查给定路径是否为在线视频URL
  /// 支持常见的网络协议：http, https, ftp, rtmp, rtsp等
  bool _isOnlineVideoUrl(String videoPath) {
    if (videoPath.isEmpty) return false;

    // 检查常见的网络协议
    final commonProtocols = [
      'http://',
      'https://',
      'ftp://',
      'ftps://',
      'rtmp://',
      'rtmps://',
      'rtsp://',
      'rtsps://',
      'mms://',
      'mmsh://',
      'mmst://',
      'udp://',
      'tcp://',
      'file://',  // 虽然是本地协议，但通常表示网络共享文件
    ];

    final lowerCasePath = videoPath.toLowerCase();
    for (final protocol in commonProtocols) {
      if (lowerCasePath.startsWith(protocol)) {
        return true;
      }
    }

    return false;
  }

  Future<void> selectRootDir() async {
    // 首先检查权限
    if (Platform.isAndroid) {
      var storageStatus = await Permission.storage.status;
      if (!storageStatus.isGranted) {
        // Android 10 及以上需要额外权限
        if (await Permission.manageExternalStorage.status.isDenied) {
          var status = await Permission.manageExternalStorage.request();
          if (!status.isGranted) {
            throw Exception('videoNotes.error.filePermissionRequired'.tr);
          }
        }
      }
    }
    String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
      dialogTitle: 'videoNotes.dialog.selectRootFolder'.tr,
    );
    if (selectedDirectory != null) {
      rootDir.value = selectedDirectory;
      _storage.write(
          StorageBox.videoNotes, VideoNotesStorageKeys.rootDir, rootDir.value);
    }
  }

  // 添加视频到播放列表
  void addToPlaylist(String url, {String? title, String? subtitlePath}) {
    if (!playlist
        .any((item) => path.normalize(item['url']) == path.normalize(url))) {
      playlist.add({
        'url': url,
        'title': title ?? _getFileNameFromUrl(url),
        'subtitlePath': subtitlePath,
      });
    }
  }

  // 从播放列表中移除
  void removeFromPlaylist(int index) {
    playlist.removeAt(index);
  }

  // 清空播放列表
  void clearPlaylist() {
    playlist.clear();
  }

  // 从URL中提取文件名
  String _getFileNameFromUrl(String url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 处理网络URL
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        // 获取最后一个路径段并解码
        String fileName = Uri.decodeComponent(pathSegments.last);
        // 如果文件名包含查询参数，去除它们
        return fileName.split('?')[0];
      }
    }

    // 处理本地文件路径
    return path.basename(url);
  }

  void toggleFullscreen() {
    isFullScreen.value = !isFullScreen.value;
  }

  String getRealName(String pickOrSaveCachedFileName) {
    int indexOfExtDot = pickOrSaveCachedFileName.lastIndexOf('.');
    if (indexOfExtDot == -1) {
      return pickOrSaveCachedFileName;
    } else {
      String fileExt =
          pickOrSaveCachedFileName.substring(indexOfExtDot).toLowerCase();
      String fileNameWithoutExtension = pickOrSaveCachedFileName.substring(
          0, pickOrSaveCachedFileName.length - fileExt.length);
      int indexOfRandomNumDot = fileNameWithoutExtension.lastIndexOf('.');
      if (indexOfRandomNumDot == -1) {
        return pickOrSaveCachedFileName;
      } else {
        String dotAndRandomNum = fileNameWithoutExtension
            .substring(indexOfRandomNumDot)
            .toLowerCase();
        String fileNameWithoutDotAndRandomNumAndExtension =
            fileNameWithoutExtension.substring(
                0, fileNameWithoutExtension.length - dotAndRandomNum.length);
        return path
            .basename(fileNameWithoutDotAndRandomNumAndExtension + fileExt);
      }
    }
  }

  // 打开网页视频
  Future<String?> openWebpageVideo() async {
    webviewController.loadUrl("https://www.bilibili.com/");
    return null;
  }

  // 打开本地视频
  Future<String?> openLocalVideo() async {
    try {
      // 首先检查权限
      if (Platform.isAndroid) {
        var storageStatus = await Permission.storage.status;
        if (!storageStatus.isGranted) {
          // Android 10 及以上需要额外权限
          if (await Permission.manageExternalStorage.status.isDenied) {
            var status = await Permission.manageExternalStorage.request();
            if (!status.isGranted) {
              throw Exception('videoNotes.error.filePermissionRequired'.tr);
            }
          }
        }
      }
      rootDir.value = _storage.read(
          StorageBox.videoNotes, VideoNotesStorageKeys.rootDir, "");
      logger.d("rootDir: ${rootDir.value}");
      if (Platform.isIOS) {
        final appDocDir = await getApplicationDocumentsDirectory();
        rootDir.value = path.join(appDocDir.path, 'Videos');
        _storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.rootDir,
            rootDir.value);
      }
      // 确保rootDir存在
      if (Platform.isAndroid || Platform.isIOS) {
        if (rootDir.value.isEmpty) {
          Get.showSnackbar(GetSnackBar(
            message: 'videoNotes.error.setRootDirectoryFirst'.tr,
            duration: const Duration(seconds: 2),
          ));
          return null;
        }
        // 创建目标目录（如果不存在）
        final Directory directory = Directory(rootDir.value);
        if (!directory.existsSync()) {
          directory.createSync(recursive: true);
        }
      }

      if (Platform.isAndroid) {
        List<String>? result = await PickOrSave().filePicker(
          params: FilePickerParams(
              allowedExtensions: ['.mp4', '.mov', '.avi', '.mkv'],
              getCachedFilePath: true,
              enableMultipleSelection: true),
        );
        if (result == null) return null;

        // 处理所有选中的文件
        var nextVideoUrl = "";
        for (var file in result) {
          // final String fileName = path.basename(Uri.decodeFull(file));
          final String fileName = getRealName(file);
          String targetPath = path.join(rootDir.value, fileName);
          if (await PathUtils.isOldAndroid) {
            targetPath = path.join(await PathUtils.downloadDir, fileName);
          }
          // logger.d("fileName: $fileName");
          // logger.d("rootDir: ${rootDir.value}");
          // logger.d("targetPath: $targetPath");
          if (!File(targetPath).existsSync()) {
            // 将文件复制到目标目录
            await File(file).copy(targetPath);
          } else {
            logger.i('File already exists at: $targetPath');
          }

          // Add to playlist
          addToPlaylist(targetPath, title: fileName);
          if (nextVideoUrl.isEmpty) {
            nextVideoUrl = targetPath;
          }
        }
        if (nextVideoUrl.isNotEmpty) {
          player.value.open(Media(nextVideoUrl));
        }

        Get.showSnackbar(GetSnackBar(
          message: 'videoNotes.message.videoAdded'.tr,
          duration: const Duration(seconds: 2),
        ));
      } else if (Platform.isIOS) {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: ['mp4', 'mov', 'avi', 'mkv'],
            allowMultiple: true,
            compressionQuality: 0);
        if (result == null) return null;
        List<String> files =
            result.paths.where((path) => path != null).cast<String>().toList();
        var nextVideoUrl = "";
        for (var file in files) {
          if (useRelativePath.value) {
            final String fileName =
                result.files.firstWhere((f) => f.path == file).name;
            final String targetPath = path.join(rootDir.value, fileName);
            if (!File(targetPath).existsSync()) {
              // 将文件复制到目标目录
              await File(file).copy(targetPath);
            } else {
              logger.i('File already exists at: $targetPath');
            }

            // Add to playlist
            addToPlaylist(targetPath, title: fileName);
            if (nextVideoUrl.isEmpty) {
              nextVideoUrl = targetPath;
            }
          } else {
            addToPlaylist(file);
            if (nextVideoUrl.isEmpty) {
              nextVideoUrl = file;
            }
          }
        }
        if (nextVideoUrl.isNotEmpty) {
          player.value.open(Media(nextVideoUrl));
        }

        Get.showSnackbar(GetSnackBar(
          message: 'videoNotes.message.videoAdded'.tr,
          duration: const Duration(seconds: 2),
        ));
      } else {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: ['mp4', 'mov', 'avi', 'mkv'],
            allowMultiple: true,
            compressionQuality: 0);
        if (result == null) return null;
        List<String> files =
            result.paths.where((path) => path != null).cast<String>().toList();
        var nextVideoUrl = "";
        for (var file in files) {
          // Add to playlist
          addToPlaylist(file);
          if (nextVideoUrl.isEmpty) {
            nextVideoUrl = file;
          }
        }
        if (nextVideoUrl.isNotEmpty) {
          player.value.open(Media(nextVideoUrl));
        }

        Get.showSnackbar(GetSnackBar(
          message: 'videoNotes.message.videoAdded'.tr,
          duration: const Duration(seconds: 2),
        ));
      }

      return playlist.isNotEmpty ? playlist.first['url'] : null;
    } catch (e) {
      logger.e('Error picking file: $e');
      Get.showSnackbar(GetSnackBar(
        message: '${'videoNotes.error.processVideoError'.tr}: $e',
        duration: const Duration(seconds: 2),
      ));
      return null;
    }
  }

  // 打开网络视频
  Future<void> openNetworkVideo() async {
    final TextEditingController urlController = TextEditingController();
    final TextEditingController titleController = TextEditingController();

    final result = await Get.dialog<Map<String, String>>(
      AlertDialog(
        title: Text('videoNotes.dialog.addNetworkVideo'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: urlController,
              decoration: InputDecoration(
                labelText: 'videoNotes.dialog.videoUrl'.tr,
                hintText: 'videoNotes.dialog.videoUrlHint'.tr,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: titleController,
              decoration: InputDecoration(
                labelText: 'videoNotes.dialog.videoTitle'.tr,
                hintText: 'videoNotes.dialog.videoTitleHint'.tr,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('videoNotes.dialog.cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              if (urlController.text.isNotEmpty) {
                Get.back(result: {
                  'url': urlController.text,
                  'title': titleController.text.isEmpty
                      ? urlController.text
                      : titleController.text,
                });
              }
            },
            child: Text('videoNotes.dialog.confirm'.tr),
          ),
        ],
      ),
    );

    if (result != null) {
      addToPlaylist(result['url']!, title: result['title']!);
      player.value.open(Media(result['url']!));
    }
  }

  // 打开播放列表
  Future<void> openPlaylist() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        compressionQuality: 0);

    if (result != null) {
      final file = File(result.files.single.path!);
      try {
        final content = await file.readAsString();
        final List<dynamic> jsonList = json.decode(content);
        playlist.clear();
        playlist
            .addAll(jsonList.map((item) => Map<String, dynamic>.from(item)));

        if (playlist.isNotEmpty) {
          final firstVideo = playlist.first;
          player.value.open(Media(firstVideo['url'] as String));
        }
        Get.showSnackbar(
          GetSnackBar(
            message: 'videoNotes.playlist.loaded'.tr,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
          ),
        );
      } catch (e) {
        Get.showSnackbar(
          GetSnackBar(
            message: 'videoNotes.playlist.loadFailed'.tr,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
          ),
        );
      }
    }
  }

  // 保存播放列表
  Future<void> saveCurrentPlaylist() async {
    if (playlist.isEmpty) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.playlist.empty'.tr,
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
      return;
    }
    try {
      // 先将数据转换为 JSON 字符串，再转换为 Uint8List
      final jsonString = jsonEncode(playlist.toList());
      final bytes = Uint8List.fromList(utf8.encode(jsonString));

      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'videoNotes.file.savePlaylist'.tr,
        fileName: 'playlist.json',
        type: FileType.custom,
        allowedExtensions: ['json'],
        bytes: bytes, // 添加 bytes 参数
      );

      logger.d("outputFile: $outputFile");
      if (outputFile != null) {
        Get.showSnackbar(
          GetSnackBar(
            message: 'videoNotes.playlist.saved'.tr,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
          ),
        );
      }
    } catch (e) {
      logger.e("保存播放列表失败: $e");
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.playlist.saveFailed'.tr,
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
    }
  }

  // 从文件加载播放列表
  Future<void> loadSavedPlaylist() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['json'],
          dialogTitle: 'videoNotes.file.selectPlaylist'.tr,
          compressionQuality: 0);

      if (result == null || result.files.isEmpty) {
        return;
      }

      final file = File(result.files.first.path!);
      final content = await file.readAsString();
      final List<dynamic> savedList = jsonDecode(content);

      playlist.clear();
      playlist.addAll(savedList.cast<Map<String, dynamic>>()
          as Iterable<Map<String, String>>);
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.playlist.loadSuccess'.tr,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.playlist.loadError'.trParams({'error': e.toString()}),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void playVideo(int index) {
    if (index >= 0 && index < playlist.length) {
      final video = playlist[index];
      logger.d('播放视频：${video['url']}');
      player.value.open(Media(video['url']!));

      // 处理字幕
      final subtitleController = Get.find<SubtitleController>();
      // 如果新视频有字幕文件，则加载；否则清空字幕
      if (video['subtitlePath'] != null &&
          video['subtitlePath'].toString().isNotEmpty) {
        subtitleController.loadSubtitleFromPath(video['subtitlePath']!);
      } else {
        subtitleController.clearSubtitles();
      }
    }
  }

  // B站相关
  // 打开B站视频
  Future<void> openBiliBiliVideo() async {
    isBiliLoggedIn.value = await _bilibiliAPI.checkLoginStatus();
    logger.d("isBiliLoggedIn: ${isBiliLoggedIn.value}");
    if (isBiliLoggedIn.value) {
      final TextEditingController urlController = TextEditingController();
      final TextEditingController titleController = TextEditingController();

      final result = await Get.dialog<Map<String, String>>(
        AlertDialog(
          title: Text('videoNotes.dialog.addBilibiliVideo'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: urlController,
                decoration: InputDecoration(
                  labelText: 'videoNotes.dialog.videoUrl'.tr,
                  hintText: 'videoNotes.dialog.videoUrlHint'.tr,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('videoNotes.dialog.cancel'.tr),
            ),
            TextButton(
              onPressed: () {
                if (urlController.text.isNotEmpty) {
                  Get.back(result: {
                    'url': urlController.text,
                    'title': titleController.text.isEmpty
                        ? urlController.text
                        : titleController.text,
                  });
                }
              },
              child: Text('videoNotes.dialog.confirm'.tr),
            ),
          ],
        ),
      );
      if (result != null) {
        final videoId = await _bilibiliAPI.extractBiliVideoId(result['url']!);
        logger.d("videoId: $videoId");
        if (videoId != null) {
          // 判断是BV号还是AV号
          String? bvid;
          String? aid;
          if (videoId.startsWith('BV')) {
            bvid = videoId;
          } else if (videoId.startsWith('av')) {
            aid = videoId.substring(2); // 去掉'av'前缀
          }

          // 获取视频信息
          final videoInfo = await _bilibiliAPI.getBiliVideoInfo(
            bvid: bvid,
            aid: aid,
          );
          logger.d("videoInfo: $videoInfo");
          if (videoInfo != null) {
            // 获取视频标题
            final title = videoInfo['title'] ?? result['title']!;
            // 获取视频cid
            final cid = videoInfo['cid'].toString();

            // 获取视频流信息
            final streamInfo = await _bilibiliAPI.getBiliVideoStream(
              bvid: bvid,
              aid: aid,
              cid: cid,
              cookie: biliCookie.value,
            );
            logger.d("streamInfo: $streamInfo");
            if (streamInfo != null && streamInfo['urls'] != null) {
              final videoUrl = streamInfo['urls'][0] as String;
              addToPlaylist(videoUrl, title: title);
              player.value.open(Media(videoUrl));
            }
          }
        }
      }
    } else {
      Get.toNamed("/bilibili-login");
    }
  }

  // 保存登录状态和Cookie
  void saveBiliLoginInfo(Map<String, dynamic> loginData) {
    isBiliLoggedIn.value = true;

    // 从登录数据中提取Cookie
    if (loginData['data']?['cookie_info']?['cookies'] != null) {
      final cookies = loginData['data']['cookie_info']['cookies'] as List;
      final cookieStrings = cookies
          .map((cookie) => '${cookie['name']}=${cookie['value']}')
          .toList();
      biliCookie.value = cookieStrings.join('; ');
    }

    // 保存用户信息å
    if (loginData['data']?['url'] != null) {
      biliUserInfo.value = {
        'url': loginData['data']['url'],
        'refresh_token': loginData['data']['refresh_token'],
        'timestamp': loginData['data']['timestamp'],
        // 可以根据需要添加其他字段
      };
    }

    // 可以选择将数据持久化到本地存储
    _saveToPersistentStorage();
  }

  // 清除登录状态
  void clearBiliLoginInfo() {
    isBiliLoggedIn.value = false;
    biliCookie.value = '';
    biliUserInfo.clear();
    // 清除本地存储的数据
    _clearPersistentStorage();
  }

  // 持久化存储登录信息
  void _saveToPersistentStorage() {
    // TODO: 实现持久化存储
    // 例如使用 GetStorage 或 SharedPreferences
  }

  // 清除持久化存储的登录信息
  void _clearPersistentStorage() {
    // TODO: 实现清除持久化存储
  }
  // 播放控制辅助函数
  /// 构建时间戳链接 (统一的链接生成逻辑，适用于本地和浏览器视频)
  String buildTimestampLink(String timestamp, String videoUrl) {
    logger.d('构建时间戳链接: timestamp=$timestamp, videoUrl=$videoUrl');

    // 根据useRelativePath设置转换视频路径
    final convertedVideoUrl = _convertVideoPathForLink(videoUrl);
    logger.d('转换后的视频路径: $convertedVideoUrl');

    // 构造guru2://链接
    var guruUrl = "guru2://seek?category=video&path=$convertedVideoUrl&t=$timestamp";

    // 检查是否需要URL编码 (与本地视频处理保持一致)
    final usePathEncoding = _storage.read(StorageBox.videoNotes,
            VideoNotesStorageKeys.usePathEncoding, true) ??
        true;

    if (usePathEncoding) {
      guruUrl = "guru2://seek?category=video&path=${Uri.encodeComponent(convertedVideoUrl)}&t=$timestamp";
      logger.d('使用URL编码: $guruUrl');
    } else {
      logger.d('不使用URL编码: $guruUrl');
    }

    // 获取链接格式设置 (与本地视频处理保持一致)
    final linkFormat = _storage.read(StorageBox.videoNotes,
            VideoNotesStorageKeys.linkFormat, "markdown") ??
        "markdown";

    // 根据格式生成最终链接 (与getLinkText()方法保持一致)
    var finalLink = "";
    switch (linkFormat) {
      case "markdown":
        finalLink = '[$timestamp]($guruUrl)';
        break;
      case "html":
        finalLink = '<a href="$guruUrl">$timestamp</a>';
        break;
      case "url":
        finalLink = guruUrl;
        break;
      case "custom":
        finalLink = _storage.read(StorageBox.videoNotes,
                VideoNotesStorageKeys.customLinkTemplate, "") ??
            "";
        finalLink = finalLink.replaceAll("{time}", timestamp);
        finalLink = finalLink.replaceAll("{url}", guruUrl);
        break;
      default:
        finalLink = '[$timestamp]($guruUrl)'; // 默认使用markdown格式
    }

    logger.d('生成的最终链接: $finalLink');
    return finalLink;
  }

  // 确保浏览器扩展连接可用 - 修复连接检测逻辑
  Future<bool> _ensureBrowserExtensionConnection() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final wsManager = Get.find<WebSocketManager>();

      logger.i('[$timestamp] 检查浏览器扩展连接状态');

      // 详细的连接状态检查
      logger.d('[$timestamp] WebSocket连接状态: ${wsManager.isConnected.value}');
      logger.d('[$timestamp] 总连接数: ${wsManager.connectionCount.value}');
      logger.d('[$timestamp] 浏览器扩展可用: ${wsManager.isBrowserExtensionAvailable()}');

      // 立即检查浏览器扩展是否已连接
      if (wsManager.isBrowserExtensionAvailable()) {
        logger.i('[$timestamp] 浏览器扩展已连接，可以发送命令');
        return true;
      }

      // 如果立即检查失败，等待一段时间让识别过程完成
      // 这解决了键盘快捷键在识别握手过程中被调用的时序问题
      logger.i('[$timestamp] 立即检查失败，等待浏览器扩展识别过程完成...');

      final waitResult = await wsManager.waitForBrowserExtension(
        timeout: const Duration(seconds: 2)
      );

      if (waitResult) {
        logger.i('[$timestamp] 浏览器扩展识别完成，连接可用');
        return true;
      }

      // 如果等待后仍然没有连接，显示明确的指导信息
      logger.w('[$timestamp] 浏览器扩展未连接，显示连接指导');

      // 运行快速诊断
      await _runConnectionDiagnostic();

      // 显示需要先连接浏览器扩展的提示
      wsManager.showBrowserExtensionRequiredNotification();

      return false;
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 检查浏览器扩展连接失败: $e');
      showLocalNotification(
        'videoNotes.error.connectionCheckFailed'.tr,
        'videoNotes.error.cannotVerifyBrowserExtension'.tr
      );
      return false;
    }
  }

  // 运行连接诊断
  Future<void> _runConnectionDiagnostic() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      logger.i('[$timestamp] 运行WebSocket连接诊断');

      final wsManager = Get.find<WebSocketManager>();
      final settingController = Get.find<SettingController>();

      // 基本状态检查
      logger.d('[$timestamp] 诊断 - WebSocket管理器已初始化: true');
      logger.d('[$timestamp] 诊断 - 连接状态: ${wsManager.isConnected.value}');
      logger.d('[$timestamp] 诊断 - 连接数量: ${wsManager.connectionCount.value}');
      logger.d('[$timestamp] 诊断 - 浏览器扩展可用: ${wsManager.isBrowserExtensionAvailable()}');
      logger.d('[$timestamp] 诊断 - 服务器端口: ${settingController.serverPort.value}');

      // 检查WebSocket服务器状态
      final serverRunning = await isWebSocketServerRunning();
      logger.d('[$timestamp] 诊断 - WebSocket服务器运行状态: $serverRunning');

      // 获取详细服务器状态
      final serverStatus = getWebSocketServerStatus();
      logger.d('[$timestamp] 诊断 - 服务器详细状态: $serverStatus');

    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 连接诊断失败: $e');
    }
  }

  /// 手动测试WebSocket服务器连接
  /// 用于调试浏览器扩展连接问题
  Future<void> debugWebSocketServer() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      logger.i('[$timestamp] 开始WebSocket服务器调试测试');

      // 1. 检查服务器状态
      final serverRunning = await isWebSocketServerRunning();
      logger.i('[$timestamp] 服务器运行状态: $serverRunning');

      if (!serverRunning) {
        logger.e('[$timestamp] WebSocket服务器未运行！');
        showLocalNotification(
          'videoNotes.error.webSocketServerNotRunning'.tr,
          'videoNotes.error.checkServerStartup'.tr
        );
        return;
      }

      // 2. 获取详细状态
      final serverStatus = getWebSocketServerStatus();
      logger.i('[$timestamp] 服务器详细状态: $serverStatus');

      // 3. 检查WebSocket管理器状态
      final wsManager = Get.find<WebSocketManager>();
      final connectionInfo = wsManager.getConnectionInfo();
      logger.i('[$timestamp] WebSocket管理器状态: $connectionInfo');

      // 4. 记录调试结果到日志，不显示通知
      logger.i('[$timestamp] WebSocket调试完成 - 服务器运行: $serverRunning, 连接数: ${connectionInfo['total_connections']}');

    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] WebSocket服务器调试失败: $e');
      // 调试失败只记录日志，不显示通知
    }
  }

  String getLinkText({String linkFormat = ""}) {
    final t = player.value.state.position;
    final rawVideoUrl = getCurrentVideoUrl();
    logger.i('Raw videoUrl: $rawVideoUrl');

    // 根据useRelativePath设置转换视频路径
    final videoUrl = _convertVideoPathForLink(rawVideoUrl);
    logger.i('Converted videoUrl for link: $videoUrl');

    // 转换时间格式
    String timeStr = '';
    final hours = t.inHours;
    final minutes = t.inMinutes.remainder(60);
    final seconds = t.inSeconds.remainder(60);

    if (hours > 0) {
      timeStr = '${hours.toString().padLeft(2, '0')}:';
    }
    timeStr +=
        '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    // 构造链接
    var url = "guru2://seek?category=video&path=$videoUrl&t=$timeStr";
    if (_storage.read(StorageBox.videoNotes,
            VideoNotesStorageKeys.usePathEncoding, true) ??
        true) {
      url =
          "guru2://seek?category=video&path=${Uri.encodeComponent(videoUrl)}&t=$timeStr";
    }
    if (linkFormat.isEmpty) {
      linkFormat = _storage.read(StorageBox.videoNotes,
              VideoNotesStorageKeys.linkFormat, "markdown") ??
          "markdown";
    }
    var finalLink = "";
    if (linkFormat == "markdown") {
      finalLink = '[$timeStr]($url)';
    } else if (linkFormat == "html") {
      finalLink = '<a href="$url">$timeStr</a>';
    } else if (linkFormat == "url") {
      finalLink = url;
    } else if (linkFormat == "custom") {
      finalLink = _storage.read(StorageBox.videoNotes,
              VideoNotesStorageKeys.customLinkTemplate, "") ??
          "";
      finalLink = finalLink.replaceAll("{time}", timeStr);
      finalLink = finalLink.replaceAll("{url}", url);
    }
    return finalLink;
  }

  Future<void> getTimestampLink() async {
    final clipboardController = Get.find<ClipboardController>();
    try {
      if (defaultPlayer.value == "browser") {
        // 浏览器模式下，请求浏览器插件生成时间戳
        await requestBrowserTimestamp();
      } else {
        // 内置播放器模式
    final currentVideoUrl = getCurrentVideoUrl();

      if (currentVideoUrl.isEmpty) {
        // 使用简单的snackbar而不是toast通知
        Get.showSnackbar(
          GetSnackBar(
            message: "videoNotes.notification.addVideoFirst".tr,
            duration: const Duration(seconds: 2),
          ),
        );
        return;
      }
        if (_storage.read(StorageBox.videoNotes,
                VideoNotesStorageKeys.pauseAfterCopyLink, false) ??
            false) {
          await videoController.player.pause();
        }
        final videoNoteController = Get.find<VideoNoteController>();
        final finalLink =
            videoNoteController.getLinkText(linkFormat: linkFormat.value);
        clipboardController.copyText(finalLink);
        // 成功时只记录日志，不显示通知
        logger.i('时间戳链接已复制到剪贴板: $finalLink');
        if (isAutoPaste.value) {
          await AnkiConnectController().simulatePaste();
        }
      }
    } catch (e) {
      showToastNotification(null, "videoNotes.notification.timestampCopyFailed".tr, "", type: "error");
    }
  }

  Future<void> getScreenshot() async {

    final clipboardController = Get.find<ClipboardController>();
    try {
      if (defaultPlayer.value == "browser") {
        // 浏览器模式下，请求浏览器插件截图
        await requestBrowserScreenshot();
      } else {
        // 内置播放器模式
            final currentVideoUrl = getCurrentVideoUrl();
    if (currentVideoUrl.isEmpty) {
      // 使用简单的snackbar而不是toast通知
      Get.showSnackbar(
        GetSnackBar(
          message: "videoNotes.notification.addVideoFirst".tr,
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }
        if (_storage.read(StorageBox.videoNotes,
                VideoNotesStorageKeys.pauseAfterScreenshot, false) ??
            false) {
          await videoController.player.pause();
        }
        final Uint8List? screenshot = await videoController.player.screenshot();

        if (screenshot != null) {
          clipboardController.copyImage(screenshot, "jpeg");
          // 成功时只记录日志，不显示通知
          logger.i('视频截图已复制到剪贴板');
          if (isAutoPaste.value) {
            await AnkiConnectController().simulatePaste();
          }
        } else {
          // 保留错误通知，因为这是关键操作失败
          showToastNotification(null, "videoNotes.notification.screenshotCopyFailed".tr, "", type: "error");
        }
      }
    } catch (e) {
      logger.e(e.toString());
      showToastNotification(null, "videoNotes.notification.screenshotCopyFailed".tr, "", type: "error");
    }
  }

  Future<void> annotSceenshot() async {
    try {
      final currentVideoUrl = getCurrentVideoUrl();
      if (currentVideoUrl.isEmpty) {
        // 使用简单的snackbar而不是toast通知
        Get.showSnackbar(
          GetSnackBar(
            message: "videoNotes.notification.addVideoFirst".tr,
            duration: const Duration(seconds: 2),
          ),
        );
        return;
      }
      await videoController.player.pause();
      final imageController = Get.find<ImageCardController>();
      final Uint8List? screenshot = await videoController.player.screenshot();
      imageController.byteArray.value = screenshot;
      await Get.toNamed("/video_note/annot");
    } catch (e) {
      showToastNotification(null, "videoNotes.notification.annotationFailed".tr, "", type: "error");
    }
  }

  Future<void> ocrSceenshot() async {
    try {
      final currentVideoUrl = getCurrentVideoUrl();
      if (currentVideoUrl.isEmpty) {
        // 使用简单的snackbar而不是toast通知
        Get.showSnackbar(
          GetSnackBar(
            message: "videoNotes.notification.addVideoFirst".tr,
            duration: const Duration(seconds: 2),
          ),
        );
        return;
      }

      // 暂停视频
      await videoController.player.pause();

      // 截取屏幕
      final Uint8List? screenshot = await videoController.player.screenshot();
      if (screenshot == null) {
        throw Exception("videoNotes.error.screenshotFailed".tr);
      }

      try {
        // 保存截图为临时文件
        final tempDir = await getTemporaryDirectory();
        final tempFile = File(
            '${tempDir.path}/video_screenshot_${DateTime.now().millisecondsSinceEpoch}.png');
        await tempFile.writeAsBytes(screenshot);

        logger.i("视频截图已保存到临时文件: ${tempFile.path}");

        // 直接通过参数传递图片路径，而不使用OCR控制器的imageCards
        Get.toNamed('/video_note/ocr', arguments: {'imagePath': tempFile.path});
      } catch (e) {
        logger.e("处理截图失败: $e");
        throw Exception("videoNotes.error.ocrInitFailed".tr);
      }
    } catch (e) {
      logger.e(e.toString());
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.error.textExtractionFailed'.trParams({'error': e.toString()}),
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
    }
  }

  Future<void> clozeSceenshot() async {
    try {
      final currentVideoUrl = getCurrentVideoUrl();
      if (currentVideoUrl.isEmpty) {
        // 使用简单的snackbar而不是toast通知
        Get.showSnackbar(
          GetSnackBar(
            message: "videoNotes.notification.addVideoFirst".tr,
            duration: const Duration(seconds: 2),
          ),
        );
        return;
      }
      await videoController.player.pause();
      final imageController = Get.find<ImageCardController>();
      final Uint8List? screenshot = await videoController.player.screenshot();
      imageController.byteArray.value = screenshot;
      Get.toNamed('/video_note/cloze');
    } catch (e) {
      logger.e(e.toString());
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.error.clozeImageFailed'.tr,
          duration: const Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
        ),
      );
    }
  }

  Future<void> playOrPause() async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      if (defaultPlayer.value == "browser") {
        logger.i('[$timestamp] 播放/暂停命令 - 浏览器模式');

        // 浏览器模式下，检查连接状态并发送命令
        if (!await _ensureBrowserExtensionConnection()) {
          logger.e('[$timestamp] 播放/暂停命令失败 - 浏览器扩展连接不可用');
          return;
        }

        final wsManager = Get.find<WebSocketManager>();
        wsManager.sendPlayPause();
        logger.i('[$timestamp] 播放/暂停命令已发送到浏览器插件');
      } else {
        logger.i('[$timestamp] 播放/暂停命令 - 内置播放器模式');
        // 内置播放器模式
        await videoController.player.playOrPause();
      }
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 播放/暂停命令执行失败: $e');
      showLocalNotification('videoNotes.error.operationFailed'.tr, 'videoNotes.error.playPauseCommandFailed'.tr);
    }
  }

  Future<void> seek(String t) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      if (defaultPlayer.value == "browser") {
        logger.i('[$timestamp] 跳转命令 - 浏览器模式: $t');

        // 浏览器模式下，检查连接状态并发送跳转命令
        if (!await _ensureBrowserExtensionConnection()) {
          logger.e('[$timestamp] 跳转命令失败 - 浏览器扩展连接不可用');
          return;
        }
        final wsManager = Get.find<WebSocketManager>();

        // 计算目标时间
        int targetSeconds = 0;
        bool isRelative = false;
        String actionDescription = '';

        if (t.startsWith('+') || t.startsWith('-')) {
          // 处理相对时间（如+5/-5）
          targetSeconds = int.tryParse(t) ?? 0;
          isRelative = true;
          actionDescription = targetSeconds > 0 ? '快进${targetSeconds}秒' : '快退${-targetSeconds}秒';
          logger.i('[$timestamp] 发送相对跳转命令到浏览器插件: $targetSeconds秒');
        } else if (t.contains(':')) {
          // 处理时间戳格式（如00:03或01:23:45）
          final parts = t.split(':').map((e) => int.tryParse(e) ?? 0).toList();
          if (parts.length == 2) {
            targetSeconds = parts[0] * 60 + parts[1];
          } else if (parts.length == 3) {
            targetSeconds = parts[0] * 3600 + parts[1] * 60 + parts[2];
          }
          actionDescription = '跳转到$t';
          logger.i('[$timestamp] 发送绝对跳转命令到浏览器插件: $targetSeconds秒 ($t)');
        } else {
          // 处理绝对秒数（如999）
          targetSeconds = int.tryParse(t) ?? 0;
          actionDescription = '跳转到$targetSeconds秒';
          logger.i('[$timestamp] 发送绝对跳转命令到浏览器插件: $targetSeconds秒');
        }

        // 发送跳转命令
        wsManager.sendSeek(targetSeconds, relative: isRelative);
        logger.i('[$timestamp] 跳转命令已发送: $actionDescription');
      } else {
        // 内置播放器模式
        final currentPosition = player.value.state.position;
        Duration targetPosition = currentPosition;
        if (t.startsWith('+') || t.startsWith('-')) {
          // 处理相对时间（如+5/-5）
          final seconds = int.tryParse(t) ?? 0;
          targetPosition = currentPosition + Duration(seconds: seconds);
        } else if (t.contains(':')) {
          // 处理时间戳格式（如00:03或01:23:45）
          final parts = t.split(':').map((e) => int.tryParse(e) ?? 0).toList();
          if (parts.length == 2) {
            targetPosition = Duration(minutes: parts[0], seconds: parts[1]);
          } else if (parts.length == 3) {
            targetPosition =
                Duration(hours: parts[0], minutes: parts[1], seconds: parts[2]);
          }
        } else {
          // 处理绝对秒数（如999）
          final seconds = int.tryParse(t) ?? 0;
          targetPosition = Duration(seconds: seconds);
        }

        // 确保时间不超出范围
        final duration = player.value.state.duration;
        if (targetPosition < Duration.zero) {
          targetPosition = Duration.zero;
        } else if (targetPosition > duration) {
          targetPosition = duration;
        }
        await videoController.player.seek(targetPosition);
      }
    } catch (e) {
      logger.e('Seek error: $e\n原始时间参数: $t');
    }
  }

  // 快捷键相关

  Future<void> disableAllShortcutsHandler() async {
    if (Platform.isAndroid || Platform.isIOS) {
      return;
    }
    if (disableAllShortcuts.value) {
      await hotKeyManager.unregisterAll();
      // 使用简单的snackbar而不是toast通知
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.shortcuts.allDisabled'.tr,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      await initAllShortcutsHandler();
      // 使用简单的snackbar而不是toast通知
      Get.showSnackbar(
        GetSnackBar(
          message: 'videoNotes.shortcuts.allEnabled'.tr,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> initAllShortcutsHandler() async {
    for (var name in hotkeyNames) {
      try {
        final hotKey = _storage.read(StorageBox.videoNotes, name);
        // logger.i("name: $name, hotKey: $hotKey");
        if (hotKey != null) {
          shortcutMap[name] = HotKey.fromJson(jsonDecode(hotKey));
        } else {
          shortcutMap[name] = null;
          HotKey? hotKey;
          switch (name) {
            case "insertPathLink":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyP,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertTimestampLink":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyT,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "insertScreenshot":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyS,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "annotScreenshot":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyM,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "playOrPause":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyJ,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "forward5":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyK,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "forward15":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyL,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "backward5":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyH,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
            case "backward15":
              hotKey = HotKey(
                key: PhysicalKeyboardKey.keyG,
                modifiers: [HotKeyModifier.control, HotKeyModifier.alt],
                scope: HotKeyScope.system,
              );
              shortcutMap[name] = hotKey;
              break;
          }
          if (hotKey != null) {
            _storage.write(
              StorageBox.pdfNote,
              name,
              jsonEncode(hotKey.toJson()),
            );
          }
        }
        if (shortcutMap.containsKey(name) &&
            shortcutHandlerMap.containsKey(name)) {
          registerHotKey(name, shortcutMap[name], shortcutHandlerMap[name]!);
        }
      } catch (e) {
        logger.e(e.toString());
      }
    }
  }

  // 修改热键注册的通用方法
  Future<void> registerHotKey(
    String name,
    HotKey? newHotKey,
    Future<void> Function(HotKey) handler,
  ) async {
    if (Platform.isAndroid || Platform.isIOS) {
      return;
    }
    if (newHotKey != null) {
      // 取消注册旧的热键
      if (shortcutMap.containsKey(name) && shortcutMap[name] != null) {
        // 创建一个副本来避免并发修改错误
        final registeredHotKeyList = [...hotKeyManager.registeredHotKeyList];
        List<HotKey> hotKeysToUnregister = [];
        for (var hotKey in registeredHotKeyList) {
          if (hotKey.identifier == shortcutMap[name]!.identifier) {
            // logger.w("取消注册热键: $name, ${shortcutMap[name]!.identifier}");
            hotKeysToUnregister.add(hotKey);
          }
        }
        for (var hotKey in hotKeysToUnregister) {
          await hotKeyManager.unregister(hotKey);
        }
      }
      shortcutMap[name] = newHotKey;
      await hotKeyManager.register(
        newHotKey,
        keyDownHandler: (hotKey) => handler(hotKey),
      );
      // 保存热键设置
      _storage.write(
        StorageBox.videoNotes,
        name,
        jsonEncode(newHotKey.toJson()),
      );
    } else {
      shortcutMap[name] = null;
      _storage.remove(StorageBox.videoNotes, name);
    }
  }

  void showPasteHotKeyRecorder(BuildContext context, String name) async {
    final hotKey = await HotKeyInputDialog.show(
      context,
      initialHotKey: shortcutMap[name],
    );

    if (hotKey != null) {
      registerHotKey(name, hotKey, shortcutHandlerMap[name]!);
    }
  }

// 获取人类可读的快捷键字符串
  String getHotKeyDisplayText(HotKey? hotKey) {
    if (hotKey == null) return 'videoNotes.status.notSet'.tr;
    final modifierNames = (hotKey.modifiers ?? []).map((m) {
      switch (m) {
        case HotKeyModifier.alt:
          return 'Alt';
        case HotKeyModifier.control:
          return 'Ctrl';
        case HotKeyModifier.meta:
          if (Platform.isMacOS) {
            return 'Cmd';
          } else {
            return 'Win';
          }
        case HotKeyModifier.shift:
          return 'Shift';
        case HotKeyModifier.capsLock:
          return 'CapsLock';
        case HotKeyModifier.fn:
          return 'Fn';
        default:
          return '';
      }
    }).where((name) => name.isNotEmpty);

    // 获取主键名称（去掉"Key "前缀）
    String keyName = keyNames[hotKey.physicalKey.usbHidUsage] ?? '';
    if (keyName.startsWith('Key ')) {
      keyName = keyName.substring(4);
    }
    if (keyName.startsWith('Digit ')) {
      keyName = keyName.substring(6);
    }

    // 组合所有部分
    final parts = [...modifierNames, keyName];
    return parts.join(' + ');
  }

  // 从Base64字符串中保存截图到剪贴板
  Future<void> saveScreenshotFromBase64(String base64Image) async {
    try {
      logger.i('从Base64接收截图数据，开始处理...');

      // 解码Base64字符串，移除data URL前缀
      final cleanBase64 = base64Image.replaceFirst(RegExp(r'data:image/\w+;base64,'), '');
      final Uint8List bytes = base64Decode(cleanBase64);

      logger.i('Base64解码完成，图像大小: ${bytes.length} 字节');

      // 获取ClipboardController实例
      final clipboardController = Get.find<ClipboardController>();

      // 检测图像格式（从data URL中提取）
      String imageFormat = "png"; // 默认格式
      final formatMatch = RegExp(r'data:image/(\w+);base64,').firstMatch(base64Image);
      if (formatMatch != null) {
        imageFormat = formatMatch.group(1)?.toLowerCase() ?? "png";
      }

      logger.i('检测到图像格式: $imageFormat');

      // 将图像数据复制到剪贴板（实际图像格式，不是文本）
      await clipboardController.copyImage(bytes, imageFormat);

      logger.i('图像已成功复制到剪贴板');

      // 保存图像到临时文件（用于调试和备份）
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${tempDir.path}/screenshot_$timestamp.$imageFormat');
      await file.writeAsBytes(bytes);

      logger.i('截图已保存到剪贴板和临时文件: ${file.path}');

      // 如果启用了自动粘贴，执行粘贴操作
      if (isAutoPaste.value) {
        await AnkiConnectController().simulatePaste();
      }
      // 成功时只记录日志，不显示通知
      logger.i('截图已成功保存到剪贴板');

    } catch (e) {
      logger.e('保存截图出错: $e');
      // 保留错误通知，因为这是关键操作失败
      showToastNotification(null, "videoNotes.notification.screenshotCopyFailed".tr, "videoNotes.error.cannotSaveScreenshot".trParams({'error': e.toString()}), type: "error");
    }
  }

  // 请求浏览器插件生成时间戳
  Future<void> requestBrowserTimestamp() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      logger.i('[$timestamp] 请求浏览器时间戳');

      // 检查浏览器扩展连接
      if (!await _ensureBrowserExtensionConnection()) {
        logger.e('[$timestamp] 时间戳请求失败 - 浏览器扩展连接不可用');
        return;
      }

      logger.i('[$timestamp] 发送时间戳请求到浏览器插件');

      // 记录时间戳生成开始到日志，不显示进度通知
      logger.d('[$timestamp] 开始生成浏览器时间戳链接');

      // 通过WebSocket管理器发送请求
      final wsManager = Get.find<WebSocketManager>();
      wsManager.requestTimestamp();
      logger.i('[$timestamp] 时间戳请求已发送');
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 发送时间戳请求失败: $e');
      showLocalNotification(
        'videoNotes.error.timestampRequestFailed'.tr,
        'videoNotes.error.cannotConnectToBrowserPlugin'.tr
      );
    }
  }

  // 请求浏览器截图
  Future<void> requestBrowserScreenshot() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      logger.i('[$timestamp] 请求浏览器截图');

      // 检查浏览器扩展连接
      if (!await _ensureBrowserExtensionConnection()) {
        logger.e('[$timestamp] 截图请求失败 - 浏览器扩展连接不可用');
        return;
      }

      logger.i('[$timestamp] 发送截图请求到浏览器插件');

      // 记录截图开始到日志，不显示进度通知
      logger.d('[$timestamp] 开始请求浏览器截图');

      // 通过WebSocket管理器发送请求
      final wsManager = Get.find<WebSocketManager>();
      wsManager.requestScreenshot();
      logger.i('[$timestamp] 截图请求已发送');
    } catch (e) {
      final timestamp = DateTime.now().toIso8601String();
      logger.e('[$timestamp] 发送截图请求失败: $e');
      showLocalNotification(
        'videoNotes.error.screenshotRequestFailed'.tr,
        'videoNotes.error.cannotConnectToBrowserPlugin'.tr
      );
    }
  }
}
