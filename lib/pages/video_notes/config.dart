import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'dart:io';
import 'dart:async';

class VideoSettingsPage extends StatefulWidget {
  const VideoSettingsPage({super.key});

  @override
  State<VideoSettingsPage> createState() => _VideoSettingsPageState();
}

class _VideoSettingsPageState extends State<VideoSettingsPage> {
  final controller = Get.find<VideoNoteController>();
  final settingController = Get.find<SettingController>();

  // Auto-save functionality
  Timer? _autoSaveTimer;
  bool _isAutoSaving = false;
  bool _showSavedIndicator = false;

  // Debounce duration for auto-save
  static const Duration _autoSaveDelay = Duration(milliseconds: 800);

  @override
  void initState() {
    super.initState();
    controller.loadSettings();
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    super.dispose();
  }

  /// Triggers auto-save with debouncing to avoid excessive save operations
  void _triggerAutoSave() {
    // Cancel existing timer if any
    _autoSaveTimer?.cancel();

    // Set new timer for debounced auto-save
    _autoSaveTimer = Timer(_autoSaveDelay, () async {
      await _performAutoSave();
    });
  }

  /// Performs the actual auto-save operation with error handling
  Future<void> _performAutoSave() async {
    if (_isAutoSaving) return; // Prevent concurrent saves

    setState(() {
      _isAutoSaving = true;
    });

    try {
      // Create a silent save method that doesn't show notifications
      await _saveSettingsSilently();

      // Show brief saved indicator
      setState(() {
        _showSavedIndicator = true;
      });

      // Hide the indicator after 1.5 seconds
      Timer(const Duration(milliseconds: 1500), () {
        if (mounted) {
          setState(() {
            _showSavedIndicator = false;
          });
        }
      });
    } catch (error) {
      // Handle save errors gracefully - only log, no user notification
      debugPrint('Auto-save failed: $error');
    } finally {
      setState(() {
        _isAutoSaving = false;
      });
    }
  }

  /// Save settings silently without showing notifications
  Future<void> _saveSettingsSilently() async {
    final storage = StorageManager();

    // Save all video notes settings
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.defaultPlayer,
        controller.defaultPlayer.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.longPressSpeedUp,
        controller.enableLongPressSpeed.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.showMiniProgressBar,
        controller.showMiniProgress.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.defaultVideoSpeed,
        controller.longPressSpeedRate.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.linkFormat,
        controller.linkFormat.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.customLinkTemplate,
        controller.customLinkTemplate.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.defaultDeck,
        controller.defaultDeck.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.defaultTags,
        controller.defaultTags.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.defaultClozeMode,
        controller.clozeMode.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.oneClozePeCard,
        controller.oneClozePeCard.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.pauseAfterScreenshot,
        controller.pauseAfterScreenshot.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.pauseAfterCopyLink,
        controller.pauseAfterCopyLink.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.usePathEncoding,
        controller.usePathEncoding.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.useRelativePath,
        controller.useRelativePath.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.isAutoPaste,
        controller.isAutoPaste.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.rootDir,
        controller.rootDir.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.enableDoubleTapPause,
        controller.enableDoubleTapPause.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.enableDoubleTapSeek,
        controller.enableDoubleTapSeek.value);
    storage.write(StorageBox.videoNotes, VideoNotesStorageKeys.seekSeconds,
        controller.seekSeconds.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.enableBrightnessGesture,
        controller.enableBrightnessGesture.value);
    storage.write(
        StorageBox.videoNotes,
        VideoNotesStorageKeys.enableVolumeGesture,
        controller.enableVolumeGesture.value);
  }

  /// Helper methods for consistent SnackBar notifications
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        // backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        // backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        // backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// Performs manual save operation with immediate execution and user feedback
  Future<void> _performManualSave() async {
    if (_isAutoSaving) return; // Prevent concurrent saves

    // Cancel any pending auto-save to avoid conflicts
    _autoSaveTimer?.cancel();

    setState(() {
      _isAutoSaving = true;
    });

    try {
      // Save settings immediately
      await _saveSettingsSilently();

      // Show success indicator
      setState(() {
        _showSavedIndicator = true;
      });

      // Show brief confirmation message for manual save
      _showSuccessSnackBar('videoNotes.message.settingsSaved'.tr);

      // Hide the indicator after 2 seconds
      Timer(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _showSavedIndicator = false;
          });
        }
      });
    } catch (error) {
      // Handle save errors with user notification for manual save
      debugPrint('Manual save failed: $error');

      _showErrorSnackBar('${'videoNotes.error.saveFailed'.tr}: $error');
    } finally {
      setState(() {
        _isAutoSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text("videoNotes.title.settings".tr),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.surface,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          // Manual save button
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isAutoSaving ? null : () => _performManualSave(),
            tooltip: 'Manual Save',
          ),
          // Save status indicator
          if (_isAutoSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else if (_showSavedIndicator)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 20,
              ),
            ),
        ],
      ),
      body: Obx(() => ListView(
            padding:
                const EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 8),
            children: [
              ShadCard(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 16, bottom: 0),
                title: Text('videoNotes.settings.category.playbackControl'.tr,
                    style: defaultCardTitleStyle),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShadRadioGroupCustom(
                      label: 'videoNotes.settings.player.defaultPlayer'.tr,
                      items: controller.playerList,
                      initialValue: controller.defaultPlayer.value,
                      onChanged: (value) {
                        controller.defaultPlayer.value = value;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.player.longPressSpeed'.tr,
                      initialValue: controller.enableLongPressSpeed.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.enableLongPressSpeed.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    if (controller.enableLongPressSpeed.value)
                      ShadSliderCustom(
                        label:
                            'videoNotes.settings.player.defaultSpeedValue'.tr,
                        initialValue: controller.longPressSpeedRate.value,
                        min: 0.5,
                        max: 4.0,
                        divisions: 7,
                        labelSuffix: 'x',
                        onChanged: (v) {
                          controller.longPressSpeedRate.value = v;
                          _triggerAutoSave();
                        },
                      ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.player.showMiniProgress'.tr,
                      initialValue: controller.showMiniProgress.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.showMiniProgress.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.player.doubleTapPause'.tr,
                      initialValue: controller.enableDoubleTapPause.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.enableDoubleTapPause.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.player.doubleTapSeek'.tr,
                      initialValue: controller.enableDoubleTapSeek.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.enableDoubleTapSeek.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    if (controller.enableDoubleTapSeek.value)
                      ShadSliderCustom(
                        label: 'videoNotes.settings.player.seekSeconds'.tr,
                        initialValue: controller.seekSeconds.value.toDouble(),
                        min: 3,
                        max: 30,
                        divisions: 27,
                        labelSuffix: 's',
                        onChanged: (v) {
                          controller.seekSeconds.value = v.round();
                          _triggerAutoSave();
                        },
                      ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.player.brightnessGesture'.tr,
                      initialValue: controller.enableBrightnessGesture.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.enableBrightnessGesture.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.player.volumeGesture'.tr,
                      initialValue: controller.enableVolumeGesture.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.enableVolumeGesture.value = v;
                        _triggerAutoSave();
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              ShadCard(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 16, bottom: 0),
                title: Text('videoNotes.settings.category.backlinkSettings'.tr,
                    style: defaultCardTitleStyle),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!Platform.isIOS)
                      ShadInputWithFileSelect(
                        title: 'videoNotes.settings.backlink.rootFolder'.tr,
                        placeholder: Text(
                            'videoNotes.settings.backlink.rootFolderPlaceholder'
                                .tr),
                        initialValue: [controller.rootDir.value],
                        isRequired: true,
                        isFolder: true,
                        onFilesSelected: (value) {
                          controller.rootDir.value = value.single;
                          _triggerAutoSave();
                        },
                        onValidate: (value, files) async {
                          final res =
                              await controller.validateRootDir(value, files);
                          return res;
                        },
                        onValidateError: (error) {
                          controller.rootDirError.value = error;
                        },
                      ),
                    ShadSelectCustom(
                      label: 'videoNotes.settings.backlink.linkFormat'.tr,
                      placeholder:
                          'videoNotes.settings.backlink.linkFormatPlaceholder'
                              .tr,
                      initialValue: [controller.linkFormat.value],
                      options: controller.linkFormatList,
                      isMultiple: false,
                      onChanged: (value) {
                        controller.linkFormat.value = value.single;
                        _triggerAutoSave();
                      },
                    ),
                    ShadRadioGroupCustom(
                      label: 'anki.pdf_note.config.link_protocol'.tr,
                      initialValue: controller.linkProtocol.value,
                      items: controller.linkProtocolList,
                      onChanged: (value) {
                        controller.linkProtocol.value = value;
                      },
                    ),
                    if (controller.linkFormat.value == 'custom')
                      ShadInputWithValidate(
                          label:
                              'videoNotes.settings.backlink.customTemplate'.tr,
                          placeholder:
                              'videoNotes.settings.backlink.customTemplatePlaceholder'
                                  .tr,
                          onChanged: (value) {
                            controller.customLinkTemplate.value = value;
                            _triggerAutoSave();
                          },
                          initialValue: controller.customLinkTemplate.value,
                          onValidate: (value) async {
                            if (value.isEmpty) {
                              return 'videoNotes.error.customTemplateEmpty'.tr;
                            }
                            return null;
                          }),
                    if (Platform.isWindows ||
                        Platform.isLinux ||
                        Platform.isMacOS)
                      ShadSwitchCustom(
                        label: 'videoNotes.settings.backlink.autoPaste'.tr,
                        initialValue: controller.isAutoPaste.value,
                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        onChanged: (v) {
                          controller.isAutoPaste.value = v;
                          _triggerAutoSave();
                        },
                      ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.backlink.pauseAfterScreenshot'
                          .tr,
                      initialValue: controller.pauseAfterScreenshot.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.pauseAfterScreenshot.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label:
                          'videoNotes.settings.backlink.pauseAfterCopyLink'.tr,
                      initialValue: controller.pauseAfterCopyLink.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.pauseAfterCopyLink.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label:
                          'videoNotes.settings.backlink.enablePathEncoding'.tr,
                      initialValue: controller.usePathEncoding.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.usePathEncoding.value = v;
                        _triggerAutoSave();
                      },
                    ),
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.backlink.useRelativePath'.tr,
                      initialValue: controller.useRelativePath.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.useRelativePath.value = v;
                        _triggerAutoSave();
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              if (Platform.isWindows ||
                  Platform.isLinux ||
                  Platform.isMacOS) ...[
                ShadCard(
                  padding: const EdgeInsets.only(
                      left: 16, right: 16, top: 16, bottom: 4),
                  title: Text(
                      'videoNotes.settings.category.shortcutSettings'.tr,
                      style: defaultCardTitleStyle),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShadSwitchCustom(
                        label: 'videoNotes.settings.shortcuts.disableAll'.tr,
                        initialValue: controller.disableAllShortcuts.value,
                        onChanged: (v) {
                          controller.disableAllShortcuts.value = v;
                          controller.disableAllShortcutsHandler();
                          _triggerAutoSave();
                        },
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.copyTimestamp'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(controller
                                .shortcutMap["insertTimestampLink"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "insertTimestampLink",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "insertTimestampLink",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.copyScreenshot'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["insertScreenshot"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "insertScreenshot",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "insertScreenshot",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.annotScreenshot'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["annotScreenshot"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "annotScreenshot",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "annotScreenshot",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.playPause'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["playOrPause"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "playOrPause",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "playOrPause",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text('videoNotes.settings.shortcuts.forward5'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["forward5"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "forward5",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "forward5",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.backward5'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["backward5"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "backward5",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "backward5",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.forward15'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["forward15"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "forward15",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "forward15",
                              ),
                            ),
                          ],
                        ),
                      ),
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        title: Text(
                            'videoNotes.settings.shortcuts.backward15'.tr,
                            style: defaultTitleStyle),
                        subtitle: Obx(() => Text(
                            controller.getHotKeyDisplayText(
                                controller.shortcutMap["backward15"]))),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ShadIconButtonCustom(
                              icon: Icons.delete,
                              size: 22,
                              onPressed: () => controller.registerHotKey(
                                "backward15",
                                null,
                                (hotKey) async {
                                  logger.d(hotKey);
                                },
                              ),
                            ),
                            ShadIconButtonCustom(
                              icon: Icons.edit,
                              size: 22,
                              onPressed: () =>
                                  controller.showPasteHotKeyRecorder(
                                context,
                                "backward15",
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
              ],
              ShadCard(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 16, bottom: 4),
                title: Text('videoNotes.settings.category.ankiSettings'.tr,
                    style: defaultCardTitleStyle),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShadInputWithValidate(
                        label: 'videoNotes.settings.anki.defaultDeck'.tr,
                        placeholder:
                            'videoNotes.settings.anki.defaultDeckPlaceholder'
                                .tr,
                        onChanged: (value) {
                          controller.defaultDeck.value = value;
                          _triggerAutoSave();
                        },
                        initialValue: controller.defaultDeck.value,
                        onValidate: (value) async {
                          if (value.isEmpty) {
                            return "videoNotes.error.deckNameEmpty".tr;
                          }
                          return "";
                        }),
                    ShadInputWithValidate(
                        label: 'videoNotes.settings.anki.defaultTags'.tr,
                        placeholder:
                            'videoNotes.settings.anki.defaultTagsPlaceholder'
                                .tr,
                        onChanged: (value) {
                          controller.defaultTags.value = value;
                          _triggerAutoSave();
                        },
                        initialValue: controller.defaultTags.value,
                        onValidate: (value) async {
                          return "";
                        }),

                    // 挖空模式选择
                    ShadSelectCustom(
                      label: 'videoNotes.settings.anki.defaultClozeMode'.tr,
                      placeholder:
                          'videoNotes.settings.anki.defaultClozeModeePlaceholder'
                              .tr,
                      initialValue: [controller.clozeMode.value],
                      options: controller.clozeModeList,
                      isMultiple: false,
                      onChanged: (value) {
                        controller.clozeMode.value = value.single;
                        _triggerAutoSave();
                      },
                    ),
                    // 一空一卡开关
                    ShadSwitchCustom(
                      label: 'videoNotes.settings.anki.oneClozePeCard'.tr,
                      initialValue: controller.oneClozePeCard.value,
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      onChanged: (v) {
                        controller.oneClozePeCard.value = v;
                        _triggerAutoSave();
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
          )),
    );
  }
}
